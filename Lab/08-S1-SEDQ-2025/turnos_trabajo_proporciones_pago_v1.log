This is XeTeX, Version 3.141592653-2.6-0.999997 (TeX Live 2025) (preloaded format=xelatex 2025.6.11)  11 JUN 2025 17:22
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**turnos_trabajo_proporciones_pago_v1.tex
(./turnos_trabajo_proporciones_pago_v1.tex
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-05-26>
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/article.cls
Document Class: article 2025/01/22 v1.4n Standard LaTeX document class
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2025/01/22 v1.4n Standard LaTeX file (size option)
)
\c@part=\count271
\c@section=\count272
\c@subsection=\count273
\c@subsubsection=\count274
\c@paragraph=\count275
\c@subparagraph=\count276
\c@figure=\count277
\c@table=\count278
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen148
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2025/05/18 v2.17x AMS math features
\@mathmargin=\skip51
For additional information on amsmath, use the `?' option.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2024/11/17 v2.01 AMS text
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks17
\ex@=\dimen149
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen150
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count279
LaTeX Info: Redefining \frac on input line 233.
\uproot@=\count280
\leftroot@=\count281
LaTeX Info: Redefining \overline on input line 398.
LaTeX Info: Redefining \colon on input line 409.
\classnum@=\count282
\DOTSCASE@=\count283
LaTeX Info: Redefining \ldots on input line 495.
LaTeX Info: Redefining \dots on input line 498.
LaTeX Info: Redefining \cdots on input line 619.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 721.
LaTeX Info: Redefining \Big on input line 722.
LaTeX Info: Redefining \bigg on input line 723.
LaTeX Info: Redefining \Bigg on input line 724.
\big@size=\dimen151
LaTeX Font Info:    Redeclaring font encoding OML on input line 742.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 743.
\macc@depth=\count284
LaTeX Info: Redefining \bmod on input line 904.
LaTeX Info: Redefining \pmod on input line 909.
LaTeX Info: Redefining \smash on input line 939.
LaTeX Info: Redefining \relbar on input line 969.
LaTeX Info: Redefining \Relbar on input line 970.
\c@MaxMatrixCols=\count285
\dotsspace@=\muskip17
\c@parentequation=\count286
\dspbrk@lvl=\count287
\tag@help=\toks18
\row@=\count288
\column@=\count289
\maxfields@=\count290
\andhelp@=\toks19
\eqnshift@=\dimen152
\alignsep@=\dimen153
\tagshift@=\dimen154
\tagwidth@=\dimen155
\totwidth@=\dimen156
\lineht@=\dimen157
\@envbody=\toks20
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks21
LaTeX Info: Redefining \[ on input line 2949.
LaTeX Info: Redefining \] on input line 2950.
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/unicode-math/unicode-math.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2025-05-26 L3 programming layer (loader) 
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2025-04-14 L3 backend support: XeTeX
\g__graphics_track_int=\count291
\g__pdfannot_backend_int=\count292
\g__pdfannot_backend_link_int=\count293
))
Package: unicode-math 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLaTeX
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/unicode-math/unicode-math-xetex.sty
Package: unicode-math-xetex 2023/08/13 v0.8r Unicode maths in XeLaTeX and LuaLaTeX
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-08-16 L3 Experimental document command parser
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/l3packages/l3keys2e/l3keys2e.sty
Package: l3keys2e 2024-08-16 LaTeX2e option processing using LaTeX3 keys
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/fontspec/fontspec.sty
Package: fontspec 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/fontspec/fontspec-xetex.sty
Package: fontspec-xetex 2024/05/11 v2.9e Font selection for XeLaTeX and LuaLaTeX
\l__fontspec_script_int=\count294
\l__fontspec_language_int=\count295
\l__fontspec_strnum_int=\count296
\l__fontspec_tmp_int=\count297
\l__fontspec_tmpa_int=\count298
\l__fontspec_tmpb_int=\count299
\l__fontspec_tmpc_int=\count300
\l__fontspec_em_int=\count301
\l__fontspec_emdef_int=\count302
\l__fontspec_strong_int=\count303
\l__fontspec_strongdef_int=\count304
\l__fontspec_tmpa_dim=\dimen158
\l__fontspec_tmpb_dim=\dimen159
\l__fontspec_tmpc_dim=\dimen160
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2024/12/21 v2.1c Standard LaTeX package
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/fontspec/fontspec.cfg))) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/fix-cm.sty
Package: fix-cm 2020/11/24 v1.1t fixes to LaTeX
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/ts1enc.def
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
LaTeX Encoding Info:    Redeclaring text command \capitalcedilla (encoding TS1) on input line 49.
LaTeX Encoding Info:    Redeclaring text command \capitalogonek (encoding TS1) on input line 52.
LaTeX Encoding Info:    Redeclaring text command \capitalgrave (encoding TS1) on input line 55.
LaTeX Encoding Info:    Redeclaring text command \capitalacute (encoding TS1) on input line 56.
LaTeX Encoding Info:    Redeclaring text command \capitalcircumflex (encoding TS1) on input line 57.
LaTeX Encoding Info:    Redeclaring text command \capitaltilde (encoding TS1) on input line 58.
LaTeX Encoding Info:    Redeclaring text command \capitaldieresis (encoding TS1) on input line 59.
LaTeX Encoding Info:    Redeclaring text command \capitalhungarumlaut (encoding TS1) on input line 60.
LaTeX Encoding Info:    Redeclaring text command \capitalring (encoding TS1) on input line 61.
LaTeX Encoding Info:    Redeclaring text command \capitalcaron (encoding TS1) on input line 62.
LaTeX Encoding Info:    Redeclaring text command \capitalbreve (encoding TS1) on input line 63.
LaTeX Encoding Info:    Redeclaring text command \capitalmacron (encoding TS1) on input line 64.
LaTeX Encoding Info:    Redeclaring text command \capitaldotaccent (encoding TS1) on input line 65.
LaTeX Encoding Info:    Redeclaring text command \t (encoding TS1) on input line 66.
LaTeX Encoding Info:    Redeclaring text command \capitaltie (encoding TS1) on input line 67.
LaTeX Encoding Info:    Redeclaring text command \newtie (encoding TS1) on input line 68.
LaTeX Encoding Info:    Redeclaring text command \capitalnewtie (encoding TS1) on input line 69.
LaTeX Encoding Info:    Redeclaring text symbol \textcapitalcompwordmark (encoding TS1) on input line 70.
LaTeX Encoding Info:    Redeclaring text symbol \textascendercompwordmark (encoding TS1) on input line 71.
LaTeX Encoding Info:    Redeclaring text symbol \textquotestraightbase (encoding TS1) on input line 72.
LaTeX Encoding Info:    Redeclaring text symbol \textquotestraightdblbase (encoding TS1) on input line 73.
LaTeX Encoding Info:    Redeclaring text symbol \texttwelveudash (encoding TS1) on input line 74.
LaTeX Encoding Info:    Redeclaring text symbol \textthreequartersemdash (encoding TS1) on input line 75.
LaTeX Encoding Info:    Redeclaring text symbol \textleftarrow (encoding TS1) on input line 76.
LaTeX Encoding Info:    Redeclaring text symbol \textrightarrow (encoding TS1) on input line 77.
LaTeX Encoding Info:    Redeclaring text symbol \textblank (encoding TS1) on input line 78.
LaTeX Encoding Info:    Redeclaring text symbol \textdollar (encoding TS1) on input line 79.
LaTeX Encoding Info:    Redeclaring text symbol \textquotesingle (encoding TS1) on input line 80.
LaTeX Encoding Info:    Redeclaring text command \textasteriskcentered (encoding TS1) on input line 81.
LaTeX Encoding Info:    Redeclaring text symbol \textdblhyphen (encoding TS1) on input line 92.
LaTeX Encoding Info:    Redeclaring text symbol \textfractionsolidus (encoding TS1) on input line 93.
LaTeX Encoding Info:    Redeclaring text symbol \textzerooldstyle (encoding TS1) on input line 94.
LaTeX Encoding Info:    Redeclaring text symbol \textoneoldstyle (encoding TS1) on input line 95.
LaTeX Encoding Info:    Redeclaring text symbol \texttwooldstyle (encoding TS1) on input line 96.
LaTeX Encoding Info:    Redeclaring text symbol \textthreeoldstyle (encoding TS1) on input line 97.
LaTeX Encoding Info:    Redeclaring text symbol \textfouroldstyle (encoding TS1) on input line 98.
LaTeX Encoding Info:    Redeclaring text symbol \textfiveoldstyle (encoding TS1) on input line 99.
LaTeX Encoding Info:    Redeclaring text symbol \textsixoldstyle (encoding TS1) on input line 100.
LaTeX Encoding Info:    Redeclaring text symbol \textsevenoldstyle (encoding TS1) on input line 101.
LaTeX Encoding Info:    Redeclaring text symbol \texteightoldstyle (encoding TS1) on input line 102.
LaTeX Encoding Info:    Redeclaring text symbol \textnineoldstyle (encoding TS1) on input line 103.
LaTeX Encoding Info:    Redeclaring text symbol \textlangle (encoding TS1) on input line 104.
LaTeX Encoding Info:    Redeclaring text symbol \textminus (encoding TS1) on input line 105.
LaTeX Encoding Info:    Redeclaring text symbol \textrangle (encoding TS1) on input line 106.
LaTeX Encoding Info:    Redeclaring text symbol \textmho (encoding TS1) on input line 107.
LaTeX Encoding Info:    Redeclaring text symbol \textbigcircle (encoding TS1) on input line 108.
LaTeX Encoding Info:    Redeclaring text command \textcircled (encoding TS1) on input line 109.
LaTeX Encoding Info:    Redeclaring text symbol \textohm (encoding TS1) on input line 115.
LaTeX Encoding Info:    Redeclaring text symbol \textlbrackdbl (encoding TS1) on input line 116.
LaTeX Encoding Info:    Redeclaring text symbol \textrbrackdbl (encoding TS1) on input line 117.
LaTeX Encoding Info:    Redeclaring text symbol \textuparrow (encoding TS1) on input line 118.
LaTeX Encoding Info:    Redeclaring text symbol \textdownarrow (encoding TS1) on input line 119.
LaTeX Encoding Info:    Redeclaring text symbol \textasciigrave (encoding TS1) on input line 120.
LaTeX Encoding Info:    Redeclaring text symbol \textborn (encoding TS1) on input line 121.
LaTeX Encoding Info:    Redeclaring text symbol \textdivorced (encoding TS1) on input line 122.
LaTeX Encoding Info:    Redeclaring text symbol \textdied (encoding TS1) on input line 123.
LaTeX Encoding Info:    Redeclaring text symbol \textleaf (encoding TS1) on input line 124.
LaTeX Encoding Info:    Redeclaring text symbol \textmarried (encoding TS1) on input line 125.
LaTeX Encoding Info:    Redeclaring text symbol \textmusicalnote (encoding TS1) on input line 126.
LaTeX Encoding Info:    Redeclaring text symbol \texttildelow (encoding TS1) on input line 127.
LaTeX Encoding Info:    Redeclaring text symbol \textdblhyphenchar (encoding TS1) on input line 128.
LaTeX Encoding Info:    Redeclaring text symbol \textasciibreve (encoding TS1) on input line 129.
LaTeX Encoding Info:    Redeclaring text symbol \textasciicaron (encoding TS1) on input line 130.
LaTeX Encoding Info:    Redeclaring text symbol \textacutedbl (encoding TS1) on input line 131.
LaTeX Encoding Info:    Redeclaring text symbol \textgravedbl (encoding TS1) on input line 132.
LaTeX Encoding Info:    Redeclaring text symbol \textdagger (encoding TS1) on input line 133.
LaTeX Encoding Info:    Redeclaring text symbol \textdaggerdbl (encoding TS1) on input line 134.
LaTeX Encoding Info:    Redeclaring text symbol \textbardbl (encoding TS1) on input line 135.
LaTeX Encoding Info:    Redeclaring text symbol \textperthousand (encoding TS1) on input line 136.
LaTeX Encoding Info:    Redeclaring text symbol \textbullet (encoding TS1) on input line 137.
LaTeX Encoding Info:    Redeclaring text symbol \textcelsius (encoding TS1) on input line 138.
LaTeX Encoding Info:    Redeclaring text symbol \textdollaroldstyle (encoding TS1) on input line 139.
LaTeX Encoding Info:    Redeclaring text symbol \textcentoldstyle (encoding TS1) on input line 140.
LaTeX Encoding Info:    Redeclaring text symbol \textflorin (encoding TS1) on input line 141.
LaTeX Encoding Info:    Redeclaring text symbol \textcolonmonetary (encoding TS1) on input line 142.
LaTeX Encoding Info:    Redeclaring text symbol \textwon (encoding TS1) on input line 143.
LaTeX Encoding Info:    Redeclaring text symbol \textnaira (encoding TS1) on input line 144.
LaTeX Encoding Info:    Redeclaring text symbol \textguarani (encoding TS1) on input line 145.
LaTeX Encoding Info:    Redeclaring text symbol \textpeso (encoding TS1) on input line 146.
LaTeX Encoding Info:    Redeclaring text symbol \textlira (encoding TS1) on input line 147.
LaTeX Encoding Info:    Redeclaring text symbol \textrecipe (encoding TS1) on input line 148.
LaTeX Encoding Info:    Redeclaring text symbol \textinterrobang (encoding TS1) on input line 149.
LaTeX Encoding Info:    Redeclaring text symbol \textinterrobangdown (encoding TS1) on input line 150.
LaTeX Encoding Info:    Redeclaring text symbol \textdong (encoding TS1) on input line 151.
LaTeX Encoding Info:    Redeclaring text symbol \texttrademark (encoding TS1) on input line 152.
LaTeX Encoding Info:    Redeclaring text symbol \textpertenthousand (encoding TS1) on input line 153.
LaTeX Encoding Info:    Redeclaring text symbol \textpilcrow (encoding TS1) on input line 154.
LaTeX Encoding Info:    Redeclaring text symbol \textbaht (encoding TS1) on input line 155.
LaTeX Encoding Info:    Redeclaring text symbol \textnumero (encoding TS1) on input line 156.
LaTeX Encoding Info:    Redeclaring text symbol \textdiscount (encoding TS1) on input line 157.
LaTeX Encoding Info:    Redeclaring text symbol \textestimated (encoding TS1) on input line 158.
LaTeX Encoding Info:    Redeclaring text symbol \textopenbullet (encoding TS1) on input line 159.
LaTeX Encoding Info:    Redeclaring text symbol \textservicemark (encoding TS1) on input line 160.
LaTeX Encoding Info:    Redeclaring text symbol \textlquill (encoding TS1) on input line 161.
LaTeX Encoding Info:    Redeclaring text symbol \textrquill (encoding TS1) on input line 162.
LaTeX Encoding Info:    Redeclaring text symbol \textcent (encoding TS1) on input line 163.
LaTeX Encoding Info:    Redeclaring text symbol \textsterling (encoding TS1) on input line 164.
LaTeX Encoding Info:    Redeclaring text symbol \textcurrency (encoding TS1) on input line 165.
LaTeX Encoding Info:    Redeclaring text symbol \textyen (encoding TS1) on input line 166.
LaTeX Encoding Info:    Redeclaring text symbol \textbrokenbar (encoding TS1) on input line 167.
LaTeX Encoding Info:    Redeclaring text symbol \textsection (encoding TS1) on input line 168.
LaTeX Encoding Info:    Redeclaring text symbol \textasciidieresis (encoding TS1) on input line 169.
LaTeX Encoding Info:    Redeclaring text symbol \textcopyright (encoding TS1) on input line 170.
LaTeX Encoding Info:    Redeclaring text symbol \textordfeminine (encoding TS1) on input line 171.
LaTeX Encoding Info:    Redeclaring text symbol \textcopyleft (encoding TS1) on input line 172.
LaTeX Encoding Info:    Redeclaring text symbol \textlnot (encoding TS1) on input line 173.
LaTeX Encoding Info:    Redeclaring text symbol \textcircledP (encoding TS1) on input line 174.
LaTeX Encoding Info:    Redeclaring text symbol \textregistered (encoding TS1) on input line 175.
LaTeX Encoding Info:    Redeclaring text symbol \textasciimacron (encoding TS1) on input line 176.
LaTeX Encoding Info:    Redeclaring text symbol \textdegree (encoding TS1) on input line 177.
LaTeX Encoding Info:    Redeclaring text symbol \textpm (encoding TS1) on input line 178.
LaTeX Encoding Info:    Redeclaring text symbol \texttwosuperior (encoding TS1) on input line 179.
LaTeX Encoding Info:    Redeclaring text symbol \textthreesuperior (encoding TS1) on input line 180.
LaTeX Encoding Info:    Redeclaring text symbol \textasciiacute (encoding TS1) on input line 181.
LaTeX Encoding Info:    Redeclaring text symbol \textmu (encoding TS1) on input line 182.
LaTeX Encoding Info:    Redeclaring text symbol \textparagraph (encoding TS1) on input line 183.
LaTeX Encoding Info:    Redeclaring text symbol \textperiodcentered (encoding TS1) on input line 184.
LaTeX Encoding Info:    Redeclaring text symbol \textreferencemark (encoding TS1) on input line 185.
LaTeX Encoding Info:    Redeclaring text symbol \textonesuperior (encoding TS1) on input line 186.
LaTeX Encoding Info:    Redeclaring text symbol \textordmasculine (encoding TS1) on input line 187.
LaTeX Encoding Info:    Redeclaring text symbol \textsurd (encoding TS1) on input line 188.
LaTeX Encoding Info:    Redeclaring text symbol \textonequarter (encoding TS1) on input line 189.
LaTeX Encoding Info:    Redeclaring text symbol \textonehalf (encoding TS1) on input line 190.
LaTeX Encoding Info:    Redeclaring text symbol \textthreequarters (encoding TS1) on input line 191.
LaTeX Encoding Info:    Redeclaring text symbol \texteuro (encoding TS1) on input line 192.
LaTeX Encoding Info:    Redeclaring text symbol \texttimes (encoding TS1) on input line 193.
LaTeX Encoding Info:    Redeclaring text symbol \textdiv (encoding TS1) on input line 194.
))
\g__um_fam_int=\count305
\g__um_fonts_used_int=\count306
\l__um_primecount_int=\count307
\g__um_primekern_muskip=\muskip18
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/unicode-math/unicode-math-table.tex))) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/lm/lmodern.sty
Package: lmodern 2015/05/01 v1.6.1 Latin Modern Fonts
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/lmr/m/n on input line 22.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/lmm/m/it on input line 23.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> OMS/lmsy/m/n on input line 24.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> OMX/lmex/m/n on input line 25.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 26.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/lmm/b/it on input line 27.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> OMS/lmsy/b/n on input line 28.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> OMX/lmex/m/n on input line 29.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 31.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/lmss/m/n on input line 32.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/lmr/m/it on input line 33.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> OT1/lmtt/m/n on input line 34.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/lmr/bx/n on input line 35.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/lmss/bx/n on input line 36.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/lmr/bx/it on input line 37.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> OT1/lmtt/m/n on input line 38.
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/microtype/microtype.sty
Package: microtype 2025/02/11 v3.2a Micro-typographical refinements (RS)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks22
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count308
)
\MT@toks=\toks23
\MT@tempbox=\box55
\MT@count=\count309
LaTeX Info: Redefining \noprotrusionifhmode on input line 1087.
LaTeX Info: Redefining \leftprotrusion on input line 1088.
\MT@prot@toks=\toks24
LaTeX Info: Redefining \rightprotrusion on input line 1107.
LaTeX Info: Redefining \textls on input line 1449.
\MT@outer@kern=\dimen161
LaTeX Info: Redefining \microtypecontext on input line 2053.
LaTeX Info: Redefining \textmicrotypecontext on input line 2070.
\MT@listname@count=\count310
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/microtype/microtype-xetex.def
File: microtype-xetex.def 2025/02/11 v3.2a Definitions specific to xetex (RS)
LaTeX Info: Redefining \lsstyle on input line 443.
LaTeX Info: Redefining \lslig on input line 451.
\MT@outer@space=\skip54
)
Package microtype Info: Loading configuration file microtype.cfg.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/microtype/microtype.cfg
File: microtype.cfg 2025/02/11 v3.2a microtype main configuration file (RS)
)
LaTeX Info: Redefining \microtypesetup on input line 3065.
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/parskip/parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count311
\Gm@cntv=\count312
\c@Gm@tempcnt=\count313
\Gm@bindingoffset=\dimen162
\Gm@wd@mp=\dimen163
\Gm@odd@mp=\dimen164
\Gm@even@mp=\dimen165
\Gm@layoutwidth=\dimen166
\Gm@layoutheight=\dimen167
\Gm@layouthoffset=\dimen168
\Gm@layoutvoffset=\dimen169
\Gm@dimlist=\toks25
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2024/12/31 v1.2e Enhanced LaTeX Graphics (DPC,SPQR)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 106.
)
\Gin@req@height=\dimen170
\Gin@req@width=\dimen171
)
\pandoc@box=\box56
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2025/06/10 v25.10 The multilingual framework for pdfLaTeX, LuaLaTeX and XeLaTeX
\babel@savecnt=\count314
LaTeX Encoding Info:    Redeclaring text command \ij (encoding OT1) on input line 2043.
LaTeX Encoding Info:    Redeclaring text command \IJ (encoding OT1) on input line 2045.
LaTeX Encoding Info:    Redeclaring text command \ij (encoding T1) on input line 2047.
LaTeX Encoding Info:    Redeclaring text command \IJ (encoding T1) on input line 2048.
LaTeX Encoding Info:    Ignoring declaration for text command \ij (encoding ?) on input line 2049.
LaTeX Encoding Info:    Ignoring declaration for text command \IJ (encoding ?) on input line 2051.
LaTeX Encoding Info:    Ignoring declaration for text command \SS (encoding ?) on input line 2076.
\U@D=\dimen172
\l@unhyphenated=\language4
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/babel/xebabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count315
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/babel-spanish/spanish.ldf
Language: spanish.ldf 2021/05/27 v5.0q Spanish support from the babel system
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/babel/locale/es/babel-spanish.tex
Package babel Info: Importing font and identification data for spanish
(babel)             from babel-es.ini. Reported on input line 12.
)
\es@quottoks=\toks26
\es@quotdepth=\count316
Package babel Info: Making " an active character on input line 570.
Package babel Info: Making . an active character on input line 675.
Package babel Info: Making < an active character on input line 722.
Package babel Info: Making > an active character on input line 722.
))
LaTeX Encoding Info:    Redeclaring text command \guillemotleft (encoding OT1) on input line 60.
LaTeX Encoding Info:    Redeclaring text command \guillemotright (encoding OT1) on input line 60.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/adjustbox/adjustbox.sty
Package: adjustbox 2025/02/26 v1.3c Adjusting TeX boxes (trim, clip, ...)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/xkeyval/xkeyval.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks27
\XKV@tempa@toks=\toks28
)
\XKV@depth=\count317
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/adjustbox/adjcalc.sty
Package: adjcalc 2012/05/16 v1.1 Provides advanced setlength with multiple back-ends (calc, etex, pgfmath)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/adjustbox/trimclip.sty
Package: trimclip 2025/02/21 v1.2a Trim and clip general TeX material
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/collectbox/collectbox.sty
Package: collectbox 2022/10/17 v0.4c Collect macro arguments as boxes
\collectedbox=\box57
)
\tc@llx=\dimen173
\tc@lly=\dimen174
\tc@urx=\dimen175
\tc@ury=\dimen176
Package trimclip Info: Using driver 'tc-xetex.def'.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/adjustbox/tc-xetex.def
File: tc-xetex.def 2019/01/04 v2.2 Clipping driver for xetex
))
\adjbox@Width=\dimen177
\adjbox@Height=\dimen178
\adjbox@Depth=\dimen179
\adjbox@Totalheight=\dimen180
\adjbox@pwidth=\dimen181
\adjbox@pheight=\dimen182
\adjbox@pdepth=\dimen183
\adjbox@ptotalheight=\dimen184
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/varwidth/varwidth.sty
Package: varwidth 2009/03/30 ver 0.92;  Variable-width minipages
\@vwid@box=\box58
\sift@deathcycles=\count318
\@vwid@loff=\dimen185
\@vwid@roff=\dimen186
)
==> First Aid for varwidth.sty applied!
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks29
\pgfutil@tempdima=\dimen187
\pgfutil@tempdimb=\dimen188
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box59
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks30
\pgfkeys@temptoks=\toks31
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks32
))
\pgf@x=\dimen189
\pgf@y=\dimen190
\pgf@xa=\dimen191
\pgf@ya=\dimen192
\pgf@xb=\dimen193
\pgf@yb=\dimen194
\pgf@xc=\dimen195
\pgf@yc=\dimen196
\pgf@xd=\dimen197
\pgf@yd=\dimen198
\w@pgf@writea=\write3
\r@pgf@reada=\read3
\c@pgf@counta=\count319
\c@pgf@countb=\count320
\c@pgf@countc=\count321
\c@pgf@countd=\count322
\t@pgf@toka=\toks33
\t@pgf@tokb=\toks34
\t@pgf@tokc=\toks35
\pgf@sys@id@count=\count323
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-xetex.def
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-xetex.def
File: pgfsys-xetex.def 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-dvipdfmx.def
File: pgfsys-dvipdfmx.def 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)
\pgfsys@objnum=\count324
))) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count325
\pgfsyssoftpath@bigbuffer@items=\count326
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen199
\pgfmath@count=\count327
\pgfmath@box=\box60
\pgfmath@toks=\toks36
\pgfmath@stack@operand=\toks37
\pgfmath@stack@operation=\toks38
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count328
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfint.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen256
\pgf@picmaxx=\dimen257
\pgf@picminy=\dimen258
\pgf@picmaxy=\dimen259
\pgf@pathminx=\dimen260
\pgf@pathmaxx=\dimen261
\pgf@pathminy=\dimen262
\pgf@pathmaxy=\dimen263
\pgf@xx=\dimen264
\pgf@xy=\dimen265
\pgf@yx=\dimen266
\pgf@yy=\dimen267
\pgf@zx=\dimen268
\pgf@zy=\dimen269
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen270
\pgf@path@lasty=\dimen271
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen272
\pgf@shorten@start@additional=\dimen273
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box61
\pgf@hbox=\box62
\pgf@layerbox@main=\box63
\pgf@picture@serial@count=\count329
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen274
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen275
\pgf@pt@y=\dimen276
\pgf@pt@temp=\dimen277
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen278
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen279
\pgf@sys@shading@range@num=\count330
\pgf@shadingcount=\count331
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box64
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box65
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen280
\pgf@nodesepend=\dimen281
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/utilities/pgffor.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgf/math/pgfmath.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen282
\pgffor@skip=\dimen283
\pgffor@stack=\toks39
\pgffor@toks=\toks40
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count332
\pgfplotmarksize=\dimen284
)
\tikz@lastx=\dimen285
\tikz@lasty=\dimen286
\tikz@lastxsaved=\dimen287
\tikz@lastysaved=\dimen288
\tikz@lastmovetox=\dimen289
\tikz@lastmovetoy=\dimen290
\tikzleveldistance=\dimen291
\tikzsiblingdistance=\dimen292
\tikz@figbox=\box66
\tikz@figbox@bg=\box67
\tikz@tempbox=\box68
\tikz@tempbox@bg=\box69
\tikztreelevel=\count333
\tikznumberofchildren=\count334
\tikznumberofcurrentchild=\count335
\tikz@fig@count=\count336
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count337
\pgfmatrixcurrentcolumn=\count338
\pgf@matrix@numberofcolumns=\count339
)
\tikz@expandcount=\count340
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/pgfplots/pgfplots.sty (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplots.revision.tex)
Package: pgfplots 2021/05/15 v1.18.1 Data Visualization (1.18.1)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplots.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplotscore.code.tex
\t@pgfplots@toka=\toks41
\t@pgfplots@tokb=\toks42
\t@pgfplots@tokc=\toks43
\pgfplots@tmpa=\dimen293
\c@pgfplots@coordindex=\count341
\c@pgfplots@scanlineindex=\count342
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/sys/pgfplotssysgeneric.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/libs/pgfplotslibrary.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfsupp_loader.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/libraries/pgflibraryfpu.code.tex)
Package pgfplots: loading complementary utilities for your pgf version...
\t@pgf@toka=\toks44
\t@pgf@tokb=\toks45
\t@pgf@tokc=\toks46
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfsupp_pgfutil-common-lists.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststructure.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststructureext.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsarray.code.tex
\c@pgfplotsarray@tmp=\count343
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsmatrix.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/numtable/pgfplotstableshared.code.tex
\c@pgfplotstable@counta=\count344
\t@pgfplotstable@a=\toks47
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsdeque.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.data.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.verb.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/libs/pgflibrarypgfplots.surfshading.code.tex
\c@pgfplotslibrarysurf@no=\count345
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/sys/pgflibrarypgfplots.surfshading.pgfsys-xetex.def (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/sys/pgflibrarypgfplots.surfshading.pgfsys-dvipdfmx.def
\c@pgfplotslibrarysurf@streamlen=\count346
)))) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/util/pgfplotscolormap.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/util/pgfplotscolor.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplotsstackedplots.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplotsplothandlers.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplothandler.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplotimage.code.tex))) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplots.scaling.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplotscoordprocessing.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplots.errorbars.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplots.markers.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplotsticks.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/pgfplots.paths.code.tex) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarydecorations.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/modules/pgfmoduledecorations.code.tex
\pgfdecoratedcompleteddistance=\dimen294
\pgfdecoratedremainingdistance=\dimen295
\pgfdecoratedinputsegmentcompleteddistance=\dimen296
\pgfdecoratedinputsegmentremainingdistance=\dimen297
\pgf@decorate@distancetomove=\dimen298
\pgf@decorate@repeatstate=\count347
\pgfdecorationsegmentamplitude=\dimen299
\pgfdecorationsegmentlength=\dimen300
)
\tikz@lib@dec@box=\box70
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarydecorations.pathmorphing.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorations.pathmorphing.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarydecorations.pathreplacing.code.tex (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorations.pathreplacing.code.tex)) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgfplots/libs/tikzlibrarypgfplots.contourlua.code.tex)
\pgfplots@numplots=\count348
\pgfplots@xmin@reg=\dimen301
\pgfplots@xmax@reg=\dimen302
\pgfplots@ymin@reg=\dimen303
\pgfplots@ymax@reg=\dimen304
\pgfplots@zmin@reg=\dimen305
\pgfplots@zmax@reg=\dimen306
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibraryplotmarks.code.tex
File: tikzlibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/libraries/pgflibraryplotmarks.code.tex
File: pgflibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary3d.code.tex
File: tikzlibrary3d.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrarybabel.code.tex
File: tikzlibrarybabel.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count349
\float@exts=\toks48
\float@box=\box71
\@float@everytoks=\toks49
\@floatcapt=\box72
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/bookmark/bookmark.sty
Package: bookmark 2023-12-10 v1.31 PDF bookmarks (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2025-05-20 v7.01m Hypertext links for LaTeX
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count350
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen307
\Hy@linkcounter=\count351
\Hy@pagecounter=\count352
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2025-05-20 v7.01m Hyperref: PDFDocEncoding definition (HO)
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count353
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2025-05-20 v7.01m Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `unicode' set `true' on input line 4040.
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count354
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip19
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen308
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count355
\Field@Width=\dimen309
\Fld@charsize=\dimen310
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count356
\c@Item=\count357
\c@Hfootnote=\count358
)
Package hyperref Info: Driver (autodetected): hxetex.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2025-05-20 v7.01m Hyperref driver for XeTeX
\pdfm@box=\box73
\c@Hy@AnnotLevel=\count359
\HyField@AnnotCount=\count360
\Fld@listcount=\count361
\c@bookmark@seq@number=\count362
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
) (/home/<USER>/.TinyTeX/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip55
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/bookmark/bkm-dvipdfm.def
File: bkm-dvipdfm.def 2023-12-10 v1.31 bookmark driver for dvipdfm (HO)
\BKM@id=\count363
)) (./turnos_trabajo_proporciones_pago_v1.aux
LaTeX Info: Redefining \. on input line 11.
LaTeX Info: Redefining \% on input line 11.
Package babel Info: 'spanish' activates 'spanish' shorthands.
(babel)             Reported  on input line 11.
)
\openout1 = `turnos_trabajo_proporciones_pago_v1.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 82.
LaTeX Font Info:    ... okay on input line 82.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 82.
LaTeX Font Info:    ... okay on input line 82.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 82.
LaTeX Font Info:    ... okay on input line 82.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 82.
LaTeX Font Info:    ... okay on input line 82.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 82.
LaTeX Font Info:    ... okay on input line 82.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 82.
LaTeX Font Info:    ... okay on input line 82.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 82.
LaTeX Font Info:    ... okay on input line 82.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 82.
LaTeX Font Info:    ... okay on input line 82.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 82.
LaTeX Font Info:    ... okay on input line 82.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 82.
LaTeX Font Info:    ... okay on input line 82.
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `normal'
(Font)                  OT1/lmr/m/n --> TU/lmr/m/n on input line 82.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/lmr/m/it --> TU/lmr/m/it on input line 82.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/lmr/bx/n --> TU/lmr/bx/n on input line 82.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/lmss/m/n --> TU/lmss/m/n on input line 82.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/lmss/bx/n --> TU/lmss/bx/n on input line 82.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/lmtt/m/n --> TU/lmtt/m/n on input line 82.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/lmtt/m/n --> TU/lmtt/bx/n on input line 82.

Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             Font family 'latinmodern-math.otf(0)' created for font
(fontspec)             'latinmodern-math.otf' with options
(fontspec)             [Scale=MatchLowercase,BoldItalicFont={},ItalicFont={},SmallCapsFont={},Script=Math,BoldFont={latinmodern-math.otf}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->s*[0.9999964596882403]"[latinmodern-math.otf]/OT:script=math;language=dflt;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[0.9999964596882403]"[latinmodern-math.otf]/OT:script=math;language=dflt;"

LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(0)/m/n' will be
(Font)              scaled to size 10.0pt on input line 82.

Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             Font family 'latinmodern-math.otf(1)' created for font
(fontspec)             'latinmodern-math.otf' with options
(fontspec)             [Scale=MatchLowercase,BoldItalicFont={},ItalicFont={},SmallCapsFont={},Script=Math,SizeFeatures={{Size=8.5-},{Size=6-8.5,Font=latinmodern-math.otf,Style=MathScript},{Size=-6,Font=latinmodern-math.otf,Style=MathScriptScript}},BoldFont={latinmodern-math.otf}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <8.5->s*[0.9999964596882403]"[latinmodern-math.otf]/OT:script=math;language=dflt;"<6-8.5>s*[0.9999964596882403]"[latinmodern-math.otf]/OT:script=math;language=dflt;+ssty=0;"<-6>s*[0.9999964596882403]"[latinmodern-math.otf]/OT:script=math;language=dflt;+ssty=1;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[0.9999964596882403]"[latinmodern-math.otf]/OT:script=math;language=dflt;"

LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(1)/m/n' will be
(Font)              scaled to size 10.0pt on input line 82.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 82.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/lmr/m/n --> TU/latinmodern-math.otf(1)/m/n on input line 82.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 82.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/lmr/bx/n --> TU/latinmodern-math.otf(1)/b/n on input line 82.

Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 1.000096459334209.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 1.000096459334209.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 1.000096459334209.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 1.000096459334209.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 1.000096459334209.


Package fontspec Info: 
(fontspec)             Font family 'latinmodern-math.otf(2)' created for font
(fontspec)             'latinmodern-math.otf' with options
(fontspec)             [Scale=MatchLowercase,BoldItalicFont={},ItalicFont={},SmallCapsFont={},Script=Math,SizeFeatures={{Size=8.5-},{Size=6-8.5,Font=latinmodern-math.otf,Style=MathScript},{Size=-6,Font=latinmodern-math.otf,Style=MathScriptScript}},BoldFont={latinmodern-math.otf},ScaleAgain=1.0001,FontAdjustment={\fontdimen
(fontspec)             8\font =6.77pt\relax \fontdimen 9\font =3.94pt\relax
(fontspec)             \fontdimen 10\font =4.44pt\relax \fontdimen 11\font
(fontspec)             =6.86pt\relax \fontdimen 12\font =3.45pt\relax
(fontspec)             \fontdimen 13\font =3.63pt\relax \fontdimen 14\font
(fontspec)             =3.63pt\relax \fontdimen 15\font =2.89pt\relax
(fontspec)             \fontdimen 16\font =2.47pt\relax \fontdimen 17\font
(fontspec)             =2.47pt\relax \fontdimen 18\font =2.5pt\relax
(fontspec)             \fontdimen 19\font =2.0pt\relax \fontdimen 22\font
(fontspec)             =2.5pt\relax \fontdimen 20\font =0pt\relax \fontdimen
(fontspec)             21\font =0pt\relax }].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <8.5->s*[1.000096459334209]"[latinmodern-math.otf]/OT:script=math;language=dflt;"<6-8.5>s*[1.000096459334209]"[latinmodern-math.otf]/OT:script=math;language=dflt;+ssty=0;"<-6>s*[1.000096459334209]"[latinmodern-math.otf]/OT:script=math;language=dflt;+ssty=1;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[1.000096459334209]"[latinmodern-math.otf]/OT:script=math;language=dflt;"

LaTeX Font Info:    Encoding `OMS' has changed to `TU' for symbol font
(Font)              `symbols' in the math version `normal' on input line 82.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/lmsy/m/n --> TU/latinmodern-math.otf(2)/m/n on input line 82.
LaTeX Font Info:    Encoding `OMS' has changed to `TU' for symbol font
(Font)              `symbols' in the math version `bold' on input line 82.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/lmsy/b/n --> TU/latinmodern-math.otf(2)/b/n on input line 82.

Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9998964600422715.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9998964600422715.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9998964600422715.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9998964600422715.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9999964596882403.


Package fontspec Info: 
(fontspec)             latinmodern-math scale = 0.9998964600422715.


Package fontspec Info: 
(fontspec)             Font family 'latinmodern-math.otf(3)' created for font
(fontspec)             'latinmodern-math.otf' with options
(fontspec)             [Scale=MatchLowercase,BoldItalicFont={},ItalicFont={},SmallCapsFont={},Script=Math,SizeFeatures={{Size=8.5-},{Size=6-8.5,Font=latinmodern-math.otf,Style=MathScript},{Size=-6,Font=latinmodern-math.otf,Style=MathScriptScript}},BoldFont={latinmodern-math.otf},ScaleAgain=0.9999,FontAdjustment={\fontdimen
(fontspec)             8\font =0.4pt\relax \fontdimen 9\font =2.0pt\relax
(fontspec)             \fontdimen 10\font =1.67pt\relax \fontdimen 11\font
(fontspec)             =1.11pt\relax \fontdimen 12\font =6.0pt\relax
(fontspec)             \fontdimen 13\font =0pt\relax }].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <8.5->s*[0.9998964600422715]"[latinmodern-math.otf]/OT:script=math;language=dflt;"<6-8.5>s*[0.9998964600422715]"[latinmodern-math.otf]/OT:script=math;language=dflt;+ssty=0;"<-6>s*[0.9998964600422715]"[latinmodern-math.otf]/OT:script=math;language=dflt;+ssty=1;"
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->s*[0.9998964600422715]"[latinmodern-math.otf]/OT:script=math;language=dflt;"

LaTeX Font Info:    Encoding `OMX' has changed to `TU' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 82.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/lmex/m/n --> TU/latinmodern-math.otf(3)/m/n on input line 82.
LaTeX Font Info:    Encoding `OMX' has changed to `TU' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 82.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/lmex/m/n --> TU/latinmodern-math.otf(3)/b/n on input line 82.
Package microtype Info: Patching varwidth to enable character protrusion.
\MT@vwid@leftmargin=\dimen311
\MT@vwid@rightmargin=\dimen312
LaTeX Info: Redefining \microtypecontext on input line 82.
Package microtype Info: Applying patch `item' on input line 82.
Package microtype Info: Applying patch `toc' on input line 82.
Package microtype Info: Applying patch `eqnum' on input line 82.
Package microtype Info: Applying patch `footnote' on input line 82.
Package microtype Info: Applying patch `verbatim' on input line 82.
LaTeX Info: Redefining \microtypesetup on input line 82.
Package microtype Info: Character protrusion enabled (level 2).
Package microtype Info: Using protrusion set `basicmath'.
Package microtype Info: No adjustment of tracking.
Package microtype Info: No adjustment of spacing.
Package microtype Info: No adjustment of kerning.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/microtype/mt-LatinModernRoman.cfg
File: mt-LatinModernRoman.cfg 2021/02/21 v1.1 microtype config. file: Latin Modern Roman (RS)
)
*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(72.26999pt, 469.75502pt, 72.26999pt)
* v-part:(T,H,B)=(72.26999pt, 650.43001pt, 72.26999pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=469.75502pt
* \textheight=650.43001pt
* \oddsidemargin=0.0pt
* \evensidemargin=0.0pt
* \topmargin=-37.0pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=65.0pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

LaTeX Info: Redefining \. on input line 82.
LaTeX Info: Redefining \% on input line 82.

Package pgfplots Warning: running in backwards compatibility mode (unsuitable tick labels; missing features). Consider writing \pgfplotsset{compat=1.18} into your preamble.
 on input line 82.

Package hyperref Info: Link coloring OFF on input line 82.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(1)/m/n' will be
(Font)              scaled to size 7.0pt on input line 89.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(1)/m/n' will be
(Font)              scaled to size 5.0pt on input line 89.
LaTeX Font Info:    Trying to load font information for OML+lmm on input line 89.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/lm/omllmm.fd
File: omllmm.fd 2015/05/01 v1.6.1 Font defs for Latin Modern
)
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 10.00092pt on input line 89.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 7.00064pt on input line 89.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(2)/m/n' will be
(Font)              scaled to size 5.00046pt on input line 89.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 9.99893pt on input line 89.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 6.99925pt on input line 89.
LaTeX Font Info:    Font shape `TU/latinmodern-math.otf(3)/m/n' will be
(Font)              scaled to size 4.99947pt on input line 89.
LaTeX Font Info:    Trying to load font information for U+msa on input line 89.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/microtype/mt-msa.cfg
File: mt-msa.cfg 2006/02/04 v1.1 microtype config. file: AMS symbols (a) (RS)
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 89.
(/home/<USER>/.TinyTeX/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
) (/home/<USER>/.TinyTeX/texmf-dist/tex/latex/microtype/mt-msb.cfg
File: mt-msb.cfg 2005/06/01 v1.0 microtype config. file: AMS symbols (b) (RS)
)
File: opcion_a.png Graphic file (type bmp)
<opcion_a.png>
File: opcion_b.png Graphic file (type bmp)
<opcion_b.png>


[1

]
File: opcion_c.png Graphic file (type bmp)
<opcion_c.png>
File: opcion_d.png Graphic file (type bmp)
<opcion_d.png>


[2]
File: opcion_a.png Graphic file (type bmp)
<opcion_a.png>


[3]

[4] (./turnos_trabajo_proporciones_pago_v1.aux
LaTeX Info: Redefining \. on input line 11.
LaTeX Info: Redefining \% on input line 11.
)
 ***********
LaTeX2e <2025-06-01> patch level 1
L3 programming layer <2025-05-26>
 ***********
 ) 
Here is how much of TeX's memory you used:
 40931 strings out of 470529
 944808 string characters out of 5491811
 1579851 words of memory out of 5000000
 68567 multiletter control sequences out of 15000+600000
 630330 words of font info for 68 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 90i,6n,114p,1032b,479s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on turnos_trabajo_proporciones_pago_v1.pdf (4 pages).
