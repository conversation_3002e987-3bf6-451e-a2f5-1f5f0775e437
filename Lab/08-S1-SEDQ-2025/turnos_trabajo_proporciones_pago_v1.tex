% Options for packages loaded elsewhere
\PassOptionsToPackage{unicode}{hyperref}
\PassOptionsToPackage{hyphens}{url}
%
\documentclass[
]{article}
\usepackage{amsmath,amssymb}
\usepackage{iftex}
\ifPDFTeX
  \usepackage[T1]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp} % provide euro and other symbols
\else % if luatex or xetex
  \usepackage{unicode-math} % this also loads fontspec
  \defaultfontfeatures{Scale=MatchLowercase}
  \defaultfontfeatures[\rmfamily]{Ligatures=TeX,Scale=1}
\fi
\usepackage{lmodern}
\ifPDFTeX\else
  % xetex/luatex font selection
\fi
% Use upquote if available, for straight quotes in verbatim environments
\IfFileExists{upquote.sty}{\usepackage{upquote}}{}
\IfFileExists{microtype.sty}{% use microtype if available
  \usepackage[]{microtype}
  \UseMicrotypeSet[protrusion]{basicmath} % disable protrusion for tt fonts
}{}
\makeatletter
\@ifundefined{KOMAClassName}{% if non-KOMA class
  \IfFileExists{parskip.sty}{%
    \usepackage{parskip}
  }{% else
    \setlength{\parindent}{0pt}
    \setlength{\parskip}{6pt plus 2pt minus 1pt}}
}{% if KOMA class
  \KOMAoptions{parskip=half}}
\makeatother
\usepackage{xcolor}
\usepackage[margin=1in]{geometry}
\usepackage{graphicx}
\makeatletter
\newsavebox\pandoc@box
\newcommand*\pandocbounded[1]{% scales image to fit in text height/width
  \sbox\pandoc@box{#1}%
  \Gscale@div\@tempa{\textheight}{\dimexpr\ht\pandoc@box+\dp\pandoc@box\relax}%
  \Gscale@div\@tempb{\linewidth}{\wd\pandoc@box}%
  \ifdim\@tempb\p@<\@tempa\p@\let\@tempa\@tempb\fi% select the smaller of both
  \ifdim\@tempa\p@<\p@\scalebox{\@tempa}{\usebox\pandoc@box}%
  \else\usebox{\pandoc@box}%
  \fi%
}
% Set default figure placement to htbp
\def\fps@figure{htbp}
\makeatother
\setlength{\emergencystretch}{3em} % prevent overfull lines
\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}
\setcounter{secnumdepth}{-\maxdimen} % remove section numbering
\usepackage[spanish]{babel}
\usepackage{amsmath}
\usepackage{fontspec}
\usepackage{unicode-math}
\usepackage{graphicx}
\usepackage{adjustbox}
\usepackage{tikz}
\usepackage{pgfplots}
\usetikzlibrary{3d,babel}
\usepackage{graphicx}
\usepackage{float}
\usepackage{tikz}
\usepackage{xcolor}
\usepackage{bookmark}
\IfFileExists{xurl.sty}{\usepackage{xurl}}{} % add URL line breaks if available
\urlstyle{same}
\hypersetup{
  hidelinks,
  pdfcreator={LaTeX via pandoc}}

\author{}
\date{\vspace{-2.5em}}

\begin{document}

\section{Question}\label{question}

En una empresa manufacturera, los empleados pueden trabajar cada semana
en dos turnos: diurno y nocturno. Existen dos clasificaciones para los
días de trabajo normales (de lunes a sábado) y dominicales. En el turno
diurno del domingo se paga un 15\% más que en el turno diurno de días
normales. En el turno nocturno de un día cualquiera, la hora de trabajo
se paga un 50\% más que en el turno diurno de ese mismo día.

Un trabajador laboró durante una semana 10 horas diurnas cada día. ¿Cuál
es la gráfica que representa correctamente la proporción de dinero
recibido cada día de la semana?

\subsection{Answerlist}\label{answerlist}

\begin{itemize}
\item
  \pandocbounded{\includegraphics[keepaspectratio]{opcion_a.png}}
\item
  \pandocbounded{\includegraphics[keepaspectratio]{opcion_b.png}}
\item
  \pandocbounded{\includegraphics[keepaspectratio]{opcion_c.png}}
\item
  \pandocbounded{\includegraphics[keepaspectratio]{opcion_d.png}}
\end{itemize}

\section{Solution}\label{solution}

Para resolver este problema, debemos calcular la proporción de dinero
que recibe el trabajador cada día de la semana.

\subsubsection{Análisis del problema}\label{anuxe1lisis-del-problema}

\textbf{Condiciones de pago:}

\begin{itemize}
\tightlist
\item
  Turno diurno días normales (lunes a sábado): pago base
\item
  Turno diurno domingo: pago base + 15\%
\item
  Turno nocturno cualquier día: pago diurno del día + 50\%
\end{itemize}

\textbf{Datos del trabajador:}

\begin{itemize}
\tightlist
\item
  Trabajó 10 horas diurnas cada día
\item
  Solo trabajó turnos diurnos (no nocturnos)
\end{itemize}

\subsubsection{Cálculo de
proporciones}\label{cuxe1lculo-de-proporciones}

Si consideramos el pago base del turno diurno de días normales como 1
unidad:

\textbf{Pago por día:}

\begin{itemize}
\tightlist
\item
  Lunes a Sábado: 1 unidad cada día = 6 unidades
\item
  Domingo: 1 + 15\% = 1,15 unidades
\end{itemize}

\textbf{Total semanal:} 7,15 unidades

\textbf{Proporciones por día:} - Lunes: 14\% - Martes: 14\% - Miércoles:
14\% - Jueves: 14\% - Viernes: 14\% - Sábado: 14\% - Domingo: 16,1\%

\subsubsection{Gráfica correcta}\label{gruxe1fica-correcta}

\pandocbounded{\includegraphics[keepaspectratio]{opcion_a.png}}

La gráfica correcta debe mostrar: - Lunes a Sábado: barras de igual
altura (misma proporción aprox. 14\%) - Domingo: barra más alta
(proporción aprox. 16,1\%)

\subsection{Answerlist}\label{answerlist-1}

\begin{itemize}
\tightlist
\item
  Verdadero. Esta gráfica representa correctamente las proporciones
  calculadas.
\item
  Falso. Esta gráfica muestra todos los días con la misma proporción,
  ignorando el pago extra del domingo.
\item
  Falso. Esta gráfica incorrectamente asigna pago extra también al
  sábado.
\item
  Falso. Esta gráfica muestra un patrón creciente que no corresponde a
  las condiciones del problema.
\end{itemize}

\section{Meta-information}\label{meta-information}

exname: turnos\_trabajo\_proporciones\_pago extype: schoice exsolution:
1000 exshuffle: TRUE exsection: Interpretación de gráficas -
Proporciones laborales

\end{document}
